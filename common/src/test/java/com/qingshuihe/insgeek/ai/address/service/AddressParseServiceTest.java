package com.qingshuihe.insgeek.ai.address.service;

import com.qingshuihe.insgeek.ai.address.dto.PersonDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 地址解析服务测试类
 * 验证优化后的地址解析功能
 */
@SpringBootTest
public class AddressParseServiceTest {

    @Autowired
    private AddressParseService addressParseService;

    /**
     * 测试基本的地址解析功能
     */
    @Test
    public void testBasicAddressParsing() {
        List<String> addresses = Arrays.asList(
                "深圳市南山区",
                "北京市朝阳区",
                "上海市浦东新区"
        );

        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);

        assertEquals(3, results.size());
        
        // 验证深圳市解析
        PersonDto shenzhen = results.get(0);
        assertEquals("广东省", shenzhen.getMedicareProv());
        assertEquals("深圳市", shenzhen.getMedicareCity());
        
        // 验证北京市解析
        PersonDto beijing = results.get(1);
        assertEquals("北京", beijing.getMedicareProv());
        assertEquals("北京市", beijing.getMedicareCity());
        
        // 验证上海市解析
        PersonDto shanghai = results.get(2);
        assertEquals("上海", shanghai.getMedicareProv());
        assertEquals("上海市", shanghai.getMedicareCity());
    }

    /**
     * 测试复杂地址解析 - 多城市识别和优先级选择
     */
    @Test
    public void testComplexAddressParsing() {
        List<String> addresses = Arrays.asList(
                "深圳园，北京小红门乡七号楼单元2朝阳区二03",  // 期望识别：深圳市（第一个出现）
                "深圳园北京市",  // 期望识别：北京市（更长匹配）
                "深圳北京元",    // 期望识别：深圳市（从长到短匹配）
                "深北元静"       // 期望识别：null（无有效城市）
        );

        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);

        assertEquals(4, results.size());
        
        // 测试用例1：多个城市时优先选择第一个
        PersonDto case1 = results.get(0);
        assertEquals("广东省", case1.getMedicareProv());
        assertEquals("深圳市", case1.getMedicareCity());
        
        // 测试用例2：优先选择更长的匹配（北京市）
        PersonDto case2 = results.get(1);
        assertEquals("北京", case2.getMedicareProv());
        assertEquals("北京市", case2.getMedicareCity());
        
        // 测试用例3：从长到短匹配，选择深圳市
        PersonDto case3 = results.get(2);
        assertEquals("广东省", case3.getMedicareProv());
        assertEquals("深圳市", case3.getMedicareCity());
        
        // 测试用例4：无有效城市
        PersonDto case4 = results.get(3);
        assertNull(case4.getMedicareProv());
        assertNull(case4.getMedicareCity());
    }

    /**
     * 性能测试 - 5000条数据在3秒内完成
     */
    @Test
    public void testPerformance() {
        // 准备5000条测试数据
        List<String> addresses = new ArrayList<>();
        String[] testAddresses = {
                "深圳园，北京小红门乡七号楼单元2朝阳区二03",
                "深圳园北京市",
                "深圳北京元",
                "深北元静",
                "广州市天河区珠江新城",
                "上海市浦东新区陆家嘴",
                "杭州市西湖区文三路",
                "成都市锦江区春熙路",
                "武汉市武昌区中南路",
                "南京市鼓楼区中山路"
        };

        // 生成5000条数据
        for (int i = 0; i < 5000; i++) {
            addresses.add(testAddresses[i % testAddresses.length] + "_" + i);
        }

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行解析
        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证结果
        assertEquals(5000, results.size());
        
        // 验证性能要求：不超过3秒（3000毫秒）
        assertTrue(duration < 3000, 
                String.format("解析5000条数据耗时 %d 毫秒，超过了3秒的要求", duration));
        
        System.out.println(String.format("性能测试通过：5000条数据解析耗时 %d 毫秒", duration));
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testEdgeCases() {
        List<String> addresses = Arrays.asList(
                "",           // 空字符串
                null,         // null值
                "   ",        // 空白字符
                "abcdefg",    // 无效地址
                "深",         // 部分城市名
                "深圳",       // 现在可以识别的城市名
                "深圳市深圳市" // 重复城市名
        );

        List<PersonDto> results = addressParseService.parseAddressStrings(addresses);

        assertEquals(7, results.size());
        
        // 验证空字符串和null的处理
        assertNull(results.get(0).getMedicareProv());
        assertNull(results.get(1).getMedicareProv());
        assertNull(results.get(2).getMedicareProv());
        assertNull(results.get(3).getMedicareProv());
        assertNull(results.get(4).getMedicareProv());

        // 验证"深圳"现在可以被识别
        PersonDto shenzhenResult = results.get(5);
        assertEquals("广东省", shenzhenResult.getMedicareProv());
        assertEquals("深圳市", shenzhenResult.getMedicareCity());
        
        // 验证重复城市名的处理 - 应该选择第一个出现的
        PersonDto duplicate = results.get(6);
        assertEquals("广东省", duplicate.getMedicareProv());
        assertEquals("深圳市", duplicate.getMedicareCity());
    }
}
