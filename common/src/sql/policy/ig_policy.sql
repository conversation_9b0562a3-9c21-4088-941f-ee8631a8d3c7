select *
from ig_policy a
where a.id = 2764468859851422137;

/**
  * 查询保单下的所有险种
  * 参数为保单id
 */
select i.plan_id,
       i.insured,
       i.terminal_enterprise,
       i.employment_enterprise,
       i.update_time
from ig_policy p,
     ig_insurance_plan ip,
     ig_insurance i
where p.id = ip.policy_id
  and ip.eid = i.plan_id
  and p.id = 2764468859851422137;

/**
  * 查询方案对应的保单信息
  * 参数为plan_id
 */
select p.id, p.*
from ig_policy p,
     ig_insurance_plan ip
where p.id = ip.policy_id
  and ip.eid in (2748567241654180722);

/**
  * 根据保司保单名称查询保单基本信息
 */

SELECT p.group_id      AS '企业ID',
       p.vendor_id     AS '保司ID',
       p.contract_type as '签约公司',
       p.name          as '保司保单名称',
       p.policy_number as '保司保单号',
       p.start_time    as '开始时间',
       p.end_time      as '结束时间',
       p.`status`      as '保单状态'
FROM ig_policy p
where p.policy_number = 1
   or p.`name` like concat('%', '企业', '%')
ORDER BY p.created_at DESC;

/**
  查询保单下所有的产品信息
 */
select pt.type, p1.*
from ig_policy p,
     ig_insurance_plan ip,
     ig_plan_config pc,
     ig_duty d,
     ig_product p1,
     ig_Product_type pt
where p.id = ip.policy_id
  and ip.eid = pc.plan_id
  and pc.id = d.plan_config_id
  and d.pid = p1.pid
  and p1.product_type_id = pt.id
  and p.id IN (7124505218, 7288005401, 7403473348)
  and pt.type = 1322221;
;