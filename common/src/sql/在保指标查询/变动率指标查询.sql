-- 查询历史满期人员信息
SELECT
    e.id as 'online_insured_id',gul.type as 'relation', count( DISTINCT gul.uid ) as total_count
FROM
    ig_plan a
        LEFT JOIN ig_insurance_plan b ON a.id = b.eid
        LEFT JOIN ig_policy c ON b.policy_id = c.id
        LEFT JOIN ig_online_insured_detail d ON c.id = d.policy_id
        LEFT JOIN ig_online_insured e ON d.online_insured_id = e.id
        LEFT JOIN ig_group_user_list gul ON gul.eid = a.id
WHERE
    e.id IN ( 2782245076654551253 )
  AND gul.exist_full_period_ins = 1
group by e.id,gul.type;
# 新风控
SELECT
    mbd.online_insured_id as 'online_insured_id' ,
    COUNT(DISTINCT gul.id_number) AS unique_uid_count
FROM
    ig_metric_basic_data mbd
        JOIN
    ig_group_user_list gul ON mbd.group_user_list_id = gul.id
WHERE
    mbd.online_insured_id in( 2782245076654551253)
  AND gul.exist_full_period_ins = 1
group by mbd.online_insured_id;

--   历史承保人员变动总数

SELECT
    e.id as 'online_insured_id',	count( DISTINCT uo.uid ) as total_count
FROM
    ig_plan a
        LEFT JOIN ig_insurance_plan b ON a.id = b.eid
        LEFT JOIN ig_policy c ON b.policy_id = c.id
        LEFT JOIN ig_online_insured_detail d ON c.id = d.policy_id
        LEFT JOIN ig_online_insured e ON d.online_insured_id = e.id
        LEFT JOIN ig_user_order uo ON uo.eid = a.id
WHERE
    e.id IN ( 123 )
  AND
    ( uo.start_time <> a.start_time
        OR uo.end_time <> a.end_time )
group by e.id;;

-- 当前投保单下的订单总数
SELECT
    e.id as 'online_insured_id',count( DISTINCT uo.id ) as total_count
FROM
    ig_plan a
        LEFT JOIN ig_insurance_plan b ON a.id = b.eid
        LEFT JOIN ig_policy c ON b.policy_id = c.id
        LEFT JOIN ig_online_insured_detail d ON c.id = d.policy_id
        LEFT JOIN ig_online_insured e ON d.online_insured_id = e.id
        LEFT JOIN ig_user_order uo ON uo.eid = a.id
WHERE
    e.id IN ( 123 )
group by e.id;

--   当前投保单下的总人数
SELECT
    e.id as 'online_insured_id',count( DISTINCT uo.uid ) as total_count
FROM
    ig_plan a
        LEFT JOIN ig_insurance_plan b ON a.id = b.eid
        LEFT JOIN ig_policy c ON b.policy_id = c.id
        LEFT JOIN ig_online_insured_detail d ON c.id = d.policy_id
        LEFT JOIN ig_online_insured e ON d.online_insured_id = e.id
        LEFT JOIN ig_user_order uo ON uo.eid = a.id
WHERE
    e.id IN ( 123 )
group by e.id;