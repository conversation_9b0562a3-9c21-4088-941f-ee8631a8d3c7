select *
from ig_product_rule_group;

select *
from ig_duty_split;

select *
from ig_product_rule a
where a.custome is not null;

select *
from qp_quote a
where a.id = 2791421583520104746;
select *
from qp_quote_config;
select *
from ig_plan a
where a.plan_name = 'plan-spg-2';


SELECT t1.id                                                                                      AS id,
       t1.created_at                                                                              AS created_at,
       t1.updated_at                                                                              AS updated_at,
       t1.created_by                                                                              AS created_by,
       t1.updated_by                                                                              AS updated_by,
       t1.tenant_id                                                                               AS tenant_id,
       t1.id                                                                                      AS ig_plan_config_id,
       t1.adjust_price                                                                            AS adjust_price,
       t1.benefit                                                                                 AS benefit,
       t1.category                                                                                AS category,
       t1.comments                                                                                AS comments,
       t1.client_type                                                                             AS client_type,
       t1.default_type                                                                            AS default_type,
       t1.is_have_million                                                                         AS is_have_million,
       t1.job_category                                                                            AS job_category,
       t1.name                                                                                    AS name,
       t1.plan_id                                                                                 AS plan_id,
       t1.policy_tag                                                                              AS policy_tag,
       json_object('amount', t1.pr_manday_price_amt, 'currency', t1.pr_manday_price_curr)         AS pr_manday_price,
       t1.rate                                                                                    AS rate,
       json_object('amount', t1.real_manday_price_amt, 'currency', t1.real_manday_price_curr)     AS real_manday_price,
       t1.status                                                                                  AS status,
       t1.relation                                                                                AS relation,
       t1.type                                                                                    AS type,
       json_object('amount', t1.cost_price_amt, 'currency', t1.cost_price_curr)                   AS cost_price,
       t1.eid                                                                                     AS eid,
       t1.template                                                                                AS template,
       t1.delete_flg                                                                              AS delete_flg,
       t1.code                                                                                    AS code,
       t1.promotion                                                                               AS promotion,
       t1.config_code                                                                             AS config_code,
       t1.is_salary                                                                               AS is_salary,
       t1.plan_config_type                                                                        AS plan_config_type,
       t1.business_type                                                                           AS business_type,
       t1.name_space                                                                              AS name_space,
       t1.platform                                                                                AS platform,
       t1.desc_info                                                                               AS desc_info,
       t1.label                                                                                   AS label,
       t1.structure_flag                                                                          AS structure_flag,
       json_object('amount', t1.base_price_amt, 'currency', t1.base_price_curr)                   AS base_price,
       t1.contribution_rate                                                                       AS contribution_rate,
       t1.excess_rate                                                                             AS excess_rate,
       t1.excess_distributed_rate                                                                 AS excess_distributed_rate,
       t1.special_edition                                                                         AS special_edition,
       t1.high_health_version                                                                     AS high_health_version,
       t1.health_status                                                                           AS health_status,
       t1.data_space                                                                              AS data_space,
       t1.quotation_config_id                                                                     AS quotation_config_id,
       t1.is_related_config                                                                       AS is_related_config,
       t1.id                                                                                      AS related_config_ids,
       t1.is_main_config                                                                          AS is_main_config,
       t1.insurance_type_detail                                                                   AS insurance_type_detail,
       t1.related_main_config_id                                                                  AS related_main_config_id,
       t1.id                                                                                      AS config_tag,
       json_object('amount', t1.annual_policy_limit_amt, 'currency',
                   t1.annual_policy_limit_curr)                                                   AS annual_policy_limit,
       t1.target_id                                                                               AS target_id,
       t1.non_outpatient_contribution_rate                                                        AS non_outpatient_contribution_rate,
       t1.outpatient_contribution_rate                                                            AS outpatient_contribution_rate,
       t1.id                                                                                      AS optional_configs,
       t1.is_extend_dependents                                                                    AS is_extend_dependents,
       t1.id                                                                                      AS extend_dependents_list,
       t1.main_extend_config_id                                                                   AS main_extend_config_id,
       t1.plan_config_scene                                                                       AS plan_config_scene
FROM ig_plan_config t1
WHERE plan_id IN (?)
  AND client_type IN (?, ?, ?)
  AND status = ?
  AND (t1.tenant_id IN ('2014', '1013177012690457228', '1207623015542714570') AND t1.name_space = '2000002' AND
       t1.data_space = 3000002 AND t1.delete_flg = false)
ORDER BY client_type