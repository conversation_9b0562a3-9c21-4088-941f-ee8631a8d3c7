select *
from ig_pipeline a
WHERE a.id = 2516888562449606507;

-- 根据企业id和时间查找流水线
SELECT *
FROM ig_pipeline a
WHERE a.data_id = 1342476828199263000
  AND a.created_at > '2025-08-25 15:15:50'
  AND a.created_at < '2025-09-15 16:40:00';

-- 	查找流水线节点
SELECT *
FROM ig_pipeline_node a
WHERE a.pipeline_id = '2518553240233931559';

-- 查询节点数据
select *
from ig_pipeline_node_data a
where a.pipeline_node_id = 2791424074601136218;

-- 	根据流水线号查找流水线节点
SELECT b.referer, b.sync_queue_type, b.`status`, a.*
FROM ig_pipeline_node a,
     ig_pipeline b
where a.pipeline_id = b.id
  and b.number in (
    '24ee90e4-4036-4867-820a-7f39cbbe5d0d'
    );
-- 更新流水线状态，使当前流水线可重复执行
update ig_pipeline a
set a.`status` = 0
where a.number = '0005aceb-f891-4c44-ab62-795eaa89ec18';
