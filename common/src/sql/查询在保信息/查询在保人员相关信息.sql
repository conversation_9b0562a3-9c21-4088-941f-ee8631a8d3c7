/**
  1. 查询某个方案下在保人员信息
  2. 参数为方案名称
 */
SELECT c.id,
       b.delete_flg,
       a.id_number,
       a.realname,
       c.plan_name
FROM ig_user a,
     ig_group_user_list b,
     ig_plan c
WHERE a.uid = b.uid
  AND b.eid = c.id
  AND b.`status` = 1
  AND c.plan_name =
      '中国邮政速递物流股份有限公司北京市邮件处理中心2025本人方案-续期-标品900-精选员福保障-重疾关怀（2024C版）';

/**
  1. 参数：投保人员编号
  2. 方案id
  3. 查询投保人员状态
 */
SELECT
    a.id_number,
    a.group_id,
    a.created_at,
    a.id as 'gul.id',
    a.eid,
    a.`status` AS 'gul.status',
    a.starttime AS 'gul.start_time',
    a.endtime AS 'gul.end_time',
    a.plan_config as 'gul.config_id',
    b.id as 'uo.id',
    b.state AS 'uo.`status`',
    b.start_time as 'uo.start_time',
    b.end_time as 'uo.endtime',
    c.id as 'ui.id',
    c.state AS 'ui.`status`' ,
    c.starttime as'ui.starttime',
    c.endtime as 'ui.endtime'
FROM
    ig_group_user_list a,
    ig_user_order b,
    ig_user_insurance c
WHERE
    a.id = b.group_user_list_id
  AND b.id = c.user_order_id
  and c.delete_flg = 0
  AND a.id_number in( '250825014')
  and a.eid = 2280646514852859305;

/**
  回归专用人员状态查询
 */
SELECT
    a.id_number,
    b.created_at,
    d.plan_name,
    a.`status` AS 'gul.status',
    b.state AS 'uo.`status`',
    c.state AS 'ui.`status`' ,
    d.id
FROM
    ig_group_user_list a,
    ig_user_order b,
    ig_user_insurance c ,
    ig_plan d
WHERE
    a.id = b.group_user_list_id
  AND b.id = c.user_order_id
  AND a.eid = d.id
  AND a.id_number IN ( 'GHH000001', 'GHH000002', 'GHH000003', 'GHH000004', 'GHH000005' )