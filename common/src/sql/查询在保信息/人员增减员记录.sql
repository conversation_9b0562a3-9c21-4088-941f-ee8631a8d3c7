# 查询人员增减员记录，参数是被保人的uid，以及创建时间
SELECT
    gol.create_time 操作时间,
    g.realname 企业名称,
    CASE
        gol.type
        WHEN gol.type = 1 THEN
            '增员'
        WHEN 5 THEN
            '减员'
        WHEN 6 THEN
            '自然过期' ELSE gol.type
        END 操作,
    u.realname 姓名,
    concat( '|', u.id_number ) 证件号码,
    LEFT ( gol.start_time, 10 ) 开始时间,
    LEFT ( gol.end_time, 10 ) 结束时间,
    p.plan_name 方案名称,
    IF
    ( referer = 'saas', ga.group_account, '' ) 操作账号,
    referer 操作途径,
    pu.NAME 操作人,
    gol.eid,
    gol.insured_id,
    gul.STATUS
FROM
    ig_group_operation_log gol
        LEFT JOIN ig_group g ON gol.group_id = g.uid
        LEFT JOIN ig_group_account ga ON ga.target_id = g.uid
        LEFT JOIN ig_user u ON u.uid = gol.insured_id
        LEFT JOIN ig_plan p ON gol.eid = p.id
        LEFT JOIN p_user pu ON gol.created_by = pu.id
        LEFT JOIN ig_group_user_list gul ON gul.id = gol.group_user_list_id
        LEFT JOIN ig_bill_detail bd ON bd.eid = gol.eid
        AND bd.uid = gol.insured_id
        AND bd.type =
            IF
            ( gol.type = 1, 1, 2 )
WHERE
    gol.type IN ( 1, 5 )
  AND gol.create_time > '2025-05-08'
#   AND gol.insured_id = **********
  AND u.id_number in ('220723198611122614')
ORDER BY
    gol.insured_id,
    gol.create_time;