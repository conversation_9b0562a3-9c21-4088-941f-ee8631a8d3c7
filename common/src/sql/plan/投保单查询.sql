select * from ig_plan a where a.plan_name in (
    '二次加保优化二共用保单一',
    '二次加保优化二共用保单二'
    );

-- 根据方案id查询方案投保单，保单信息
SELECT a.id             as '方案ID',
       c.id             as '保单ID',
       b.plan_config_id as '配置ID',
       e.id             as '投保单ID'
FROM ig_plan a
         LEFT JOIN ig_insurance_plan b on a.id = b.eid
         LEFT JOIN ig_policy c on b.policy_id = c.id
         LEFT JOIN ig_online_insured_detail d on c.id = d.policy_id
         LEFT JOIN ig_online_insured e on d.online_insured_id = e.id
WHERE a.id IN (
    2782245076654551196,
    2782245076654551282
    );


-- 根据方案名称查询方案投保单，保单信息
SELECT a.id             as '方案ID',
       c.id             as '保单ID',
       b.plan_config_id as '配置ID',
       e.id             as '投保单ID'
FROM ig_plan a
         LEFT JOIN ig_insurance_plan b on a.id = b.eid
         LEFT JOIN ig_policy c on b.policy_id = c.id
         LEFT JOIN ig_online_insured_detail d on c.id = d.policy_id
         LEFT JOIN ig_online_insured e on d.online_insured_id = e.id
WHERE a.plan_name IN (
    ''
    );


-- 根据配置id查询投保单id
SELECT b.plan_config_id    as '配置ID',
       d.online_insured_id as '投保单ID'
FROM ig_insurance_plan b
         LEFT JOIN ig_policy c on b.policy_id = c.id
         LEFT JOIN ig_online_insured_detail d on c.id = d.policy_id
WHERE b.plan_config_id IN (
    2739597442974646669
    );

-- 根据投保单查询所有的方案
SELECT a.id             as '方案ID',
       c.id             as '保单ID',
       b.plan_config_id as '配置ID',
       e.id             as '投保单ID'
FROM ig_plan a
         LEFT JOIN ig_insurance_plan b on a.id = b.eid
         LEFT JOIN ig_policy c on b.policy_id = c.id
         LEFT JOIN ig_online_insured_detail d on c.id = d.policy_id
         LEFT JOIN ig_online_insured e on d.online_insured_id = e.id
WHERE e.id IN (
    2748539427445971895
    );

-- 根据投保单查询对应的gul信息
SELECT
-- 	count(gul.id)
gul.uid
FROM ig_plan a
         LEFT JOIN ig_insurance_plan b on a.id = b.eid
         LEFT JOIN ig_policy c on b.policy_id = c.id
         LEFT JOIN ig_online_insured_detail d on c.id = d.policy_id
         LEFT JOIN ig_online_insured e on d.online_insured_id = e.id
         LEFT JOIN ig_group_user_list gul on gul.eid = a.id
WHERE e.id IN (
    2748539427445971895
    );


# 查询当前方案下存在投保记录的 保单和投保单信息
SELECT DISTINCT po.id as policy_id, oid.online_insured_id
FROM ig_policy po
         JOIN ig_online_insured_detail oid ON oid.policy_id = po.id
         JOIN ig_insurance_plan ip ON ip.policy_id = po.id
         JOIN ig_plan p ON p.id = ip.eid
WHERE ip.plan_config_id IN (SELECT DISTINCT gul.plan_config
                            FROM ig_group_user_list gul
                            WHERE gul.eid IN (
                                2739661712865264086
                                ));
