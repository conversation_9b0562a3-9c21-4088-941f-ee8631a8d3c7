# Excel VBA 脚本变更说明

## 变更概述

根据您的需求，对VBA脚本进行了以下两个主要变更：

1. **清空A列时同时清空同行其他列**
2. **将联动控制从A列改为P列**

## 详细变更内容

### 1. 清空A列时同时清空同行其他列

#### 新增函数：`ClearRowData`
```vba
Public Sub ClearRowData(ByVal rowNum As Long)
```
- **功能**：当A列被清空时，自动清空该行A到Z列的所有内容
- **操作**：删除该行的所有复选框，清空目标Sheet的对应内容

#### 修改函数：`OnPreSelectionChange`
- **新增逻辑**：监听A列变化，当A列被清空时自动调用`ClearRowData`函数

### 2. 将联动控制从A列改为P列

#### 修改的核心函数：

##### `UpdateCheckboxesForRow`
- **变更前**：`cellVal = Trim(wsPre.Cells(rowNum, 1).value)` (A列)
- **变更后**：`cellVal = Trim(wsPre.Cells(rowNum, 16).value)` (P列)

##### `UpdateSheetColor`
- **变更前**：`planName = Trim(CStr(wsPre.Cells(rowNum, 1).value))` (A列)
- **变更后**：`planName = Trim(CStr(wsPre.Cells(rowNum, 16).value))` (P列)

##### `UpdateAllTargetSheetHeaders`
- **变更前**：`.Formula = "=IF('Pre Selection'!A" & preSelectionRow & "="""","""", 'Pre Selection'!A" & preSelectionRow & ")"`
- **变更后**：`.Formula = "=IF('Pre Selection'!P" & preSelectionRow & "="""","""", 'Pre Selection'!P" & preSelectionRow & ")"`

##### `OnPreSelectionChange`
- **新增逻辑**：监听P列变化（第16列），当P列发生变化时触发联动操作

## 新增辅助函数

### `MigrateFromAColumnToPColumn`
- **功能**：数据迁移函数，将现有A列的数据迁移到P列
- **用途**：帮助您从旧版本平滑过渡到新版本

### `TestClearFunction`
- **功能**：测试清空功能
- **用途**：验证清空逻辑是否正常工作

## 使用说明

### 1. 部署新脚本
1. 将修改后的VBA代码复制到您的Excel工作簿中
2. 运行 `ManualInitialize()` 函数初始化所有公式

### 2. 数据迁移（如果需要）
如果您现有的数据在A列中，可以运行：
```vba
Call MigrateFromAColumnToPColumn()
```
这将自动将A列的数据迁移到P列，并清空A列。

### 3. 新的工作流程
- **联动控制**：现在通过P列来控制其他Sheet页的表头名称、颜色联动、内容清空
- **数据清空**：清空A列时，会自动清空该行的所有其他列数据和复选框

## 测试建议

1. **测试P列联动**：
   - 在P2单元格输入值，检查是否正确生成复选框和联动
   - 清空P2单元格，检查是否正确清空相关内容

2. **测试A列清空**：
   - 在有数据的行清空A列，检查是否同时清空了其他列

3. **测试复选框功能**：
   - 点击复选框，检查目标Sheet的颜色变化是否正常

## 注意事项

1. **备份数据**：在应用新脚本前，请备份您的Excel文件
2. **测试环境**：建议先在测试环境中验证所有功能
3. **P列设置**：确保P列有足够的空间用于输入计划名称
4. **兼容性**：新脚本向后兼容，但建议使用数据迁移函数进行平滑过渡

## 主要变更总结

| 功能 | 变更前 | 变更后 |
|------|--------|--------|
| 联动控制列 | A列 | P列 |
| 清空A列效果 | 仅清空A列 | 清空整行数据 |
| 表头公式引用 | 'Pre Selection'!A2 | 'Pre Selection'!P2 |
| 触发事件 | A列变化 | A列清空 + P列变化 |
