' 修改后的VBA脚本 - 主要变更：
' 1. 清空A列时同时清空同行其他列
' 2. 将联动控制从A列改为P列

' 获取所有复选框列与对应 Sheet 的映射关系
Public Function GetSheetMapping() As Object
    Dim dict As Object
    Set dict = CreateObject("Scripting.Dictionary")
    
    dict.Add "GHS", <PERSON><PERSON><PERSON>("Group Hospital & Surgical", 2, 43)
    dict.Add "GMM", A<PERSON>y("Group Major Medical", 2, 15)
    dict.Add "GP", A<PERSON>y("Outpatient GP", 2, 18)
    dict.Add "SP", Array("Outpatient SP", 2, 23)
    dict.Add "GD", <PERSON><PERSON><PERSON>("Group Dental", 2, 8)
    dict.Add "GTL", <PERSON><PERSON>y("Group Term Life", 2, 3)
    dict.Add "GCI", Array("Group Critical Illness", 2, 2)
    dict.Add "GPA", Array("Group Personal Accident", 2, 2)
    dict.Add "GADD", Array("Group Acc Death & Dismemberment", 2, 2)
    dict.Add "GDI", <PERSON><PERSON><PERSON>("Group Disability Income", 2, 3)
    dict.Add "GM", <PERSON><PERSON><PERSON>("Group Maternity", 2, 3)
    dict.Add "O", Array("Other", 2, 10)
    
    Set GetSheetMapping = dict
End Function

' 更新某行复选框和颜色 - 修改为基于P列控制
Public Sub UpdateCheckboxesForRow(ByVal rowNum As Long)
    Dim wsPre As Worksheet
    Set wsPre = ThisWorkbook.Sheets("Pre Selection")
    
    ' 修改：现在基于P列的值来控制联动
    Dim cellVal As String
    cellVal = Trim(wsPre.Cells(rowNum, 16).value) ' P列是第16列
    
    ' 删除旧复选框
    Dim chk As CheckBox
    For Each chk In wsPre.CheckBoxes
        If Not chk Is Nothing Then
            If chk.TopLeftCell.Row = rowNum And chk.TopLeftCell.Column >= 2 And chk.TopLeftCell.Column <= 13 Then
                chk.Delete
            End If
        End If
    Next chk
    
    ' 清空 B-M 列内容和字体颜色
    Dim col As Integer
    For col = 2 To 13
        With wsPre.Cells(rowNum, col)
            .ClearContents
            .Font.Color = RGB(0, 0, 0)
        End With
    Next col
    
    ' 修改：如果P列为空，则清空目标 Sheet 的内容和颜色并退出
    If cellVal = "" Then
        Call ClearTargetSheetContentsAndColors(rowNum)
        Call UpdateAllTargetSheetHeaders
        Exit Sub
    End If
    
    ' 更新所有目标Sheet的表头公式
    Call UpdateAllTargetSheetHeaders
    
    ' 修改：如果P列有值，则添加复选框
    For col = 2 To 13
        Dim colName As String
        colName = wsPre.Cells(1, col).value ' 第一行是列名，如 GHS、GMM 等
        
        With wsPre.CheckBoxes.Add(wsPre.Cells(rowNum, col).Left + (wsPre.Cells(rowNum, col).Width - 15) / 2, _
                                  wsPre.Cells(rowNum, col).Top + (wsPre.Cells(rowNum, col).Height - 15) / 2, _
                                  15, 15)
            .Caption = ""
            .LinkedCell = wsPre.Cells(rowNum, col).Address
            .Name = "chk_" & rowNum & "_" & col
            .Placement = xlMoveAndSize
            .OnAction = "CheckboxClicked"
        End With
        
        wsPre.Cells(rowNum, col).Font.Color = RGB(255, 255, 255)
        
        ' 初始状态下更新颜色
        Call UpdateSheetColor(colName, rowNum)
    Next col
End Sub

' 更新目标 Sheet 对应列颜色 - 修改为基于P列
Public Sub UpdateSheetColor(ByVal colName As String, ByVal rowNum As Long)
    Dim wsPre As Worksheet
    Set wsPre = ThisWorkbook.Sheets("Pre Selection")
    
    Dim mapping As Object
    Set mapping = GetSheetMapping()
    
    If Not mapping.Exists(colName) Then Exit Sub
    
    Dim sheetInfo As Variant
    sheetInfo = mapping(colName)
    Dim sheetName As String: sheetName = sheetInfo(0)
    Dim rowStart As Long: rowStart = sheetInfo(1)
    Dim rowEnd As Long: rowEnd = sheetInfo(2)
    
    Dim wsTarget As Worksheet
    Set wsTarget = ThisWorkbook.Sheets(sheetName)
    
    ' 修改：现在从P列获取计划名称
    Dim planName As String
    planName = Trim(CStr(wsPre.Cells(rowNum, 16).value)) ' P列是第16列
    
    If planName = "" Then Exit Sub
    
    ' 查找目标列
    Dim targetCol As Long
    Dim cell As Range
    For Each cell In wsTarget.Range("D1:Z1")
        If Trim(CStr(cell.value)) = planName Then
            targetCol = cell.Column
            Exit For
        End If
    Next cell
    
    If targetCol = 0 Then Exit Sub
    
    Dim colIndex As Long
    colIndex = Application.Match(colName, wsPre.Rows(1), 0)
    If IsError(colIndex) Then Exit Sub
    
    Dim isChecked As Boolean
    isChecked = (wsPre.Cells(rowNum, colIndex).value = True)
    
    Dim i As Long
    For i = rowStart To rowEnd
        With wsTarget.Cells(i, targetCol)
            If isChecked Then
                .Interior.Color = RGB(198, 239, 206) ' 浅绿色
            Else
                .Interior.Color = RGB(0, 0, 0) ' 黑色
            End If
        End With
    Next i
End Sub

' 复选框点击事件
Public Sub CheckboxClicked()
    Dim chk As CheckBox
    Set chk = ActiveSheet.CheckBoxes(Application.Caller)
    
    Dim rowNum As Long
    rowNum = chk.TopLeftCell.Row
    
    Dim colNum As Long
    colNum = chk.TopLeftCell.Column
    
    Dim colName As String
    colName = ActiveSheet.Cells(1, colNum).value
    
    Call UpdateSheetColor(colName, rowNum)
End Sub

' 清空目标 Sheet 的内容和颜色 - 修改为基于P列
Public Sub ClearTargetSheetContentsAndColors(ByVal rowNum As Long)
    Dim wsPre As Worksheet
    Set wsPre = ThisWorkbook.Sheets("Pre Selection")
    
    Dim mapping As Object
    Set mapping = GetSheetMapping()
    
    ' 修改：根据rowNum计算对应的列位置，但现在基于P列的值
    Dim expectedCol As Long
    expectedCol = rowNum + 2 ' A2对应D列(4), A3对应E列(5)等
    
    ' 遍历所有目标Sheet，查找并清空对应列
    Dim key As Variant
    For Each key In mapping.Keys
        Dim sheetInfo As Variant
        sheetInfo = mapping(key)
        Dim wsTarget As Worksheet
        Set wsTarget = ThisWorkbook.Sheets(sheetInfo(0))
        Dim rowStart As Long: rowStart = sheetInfo(1)
        Dim rowEnd As Long: rowEnd = sheetInfo(2)
        
        ' 清空对应列的内容和颜色
        If expectedCol >= 4 And expectedCol <= 26 Then ' D列到Z列
            ' 清空内容和颜色
            Dim i As Long
            For i = rowStart To rowEnd
                With wsTarget.Cells(i, expectedCol)
                    .ClearContents
                    .Interior.ColorIndex = xlNone
                End With
            Next i
        End If
    Next key
End Sub

' 更新所有目标Sheet的表头公式 - 修改为基于P列
Public Sub UpdateAllTargetSheetHeaders()
    Dim mapping As Object
    Set mapping = GetSheetMapping()
    
    Dim key As Variant
    For Each key In mapping.Keys
        Dim sheetInfo As Variant
        sheetInfo = mapping(key)
        Dim wsTarget As Worksheet
        Set wsTarget = ThisWorkbook.Sheets(sheetInfo(0))
        
        ' 更新D1到Z1的公式 - 修改为引用P列
        Dim col As Long
        For col = 4 To 26 ' D列(4)到Z列(26)
            Dim preSelectionRow As Long
            preSelectionRow = col - 2 ' D1对应P2, E1对应P3, F1对应P4等
            
            With wsTarget.Cells(1, col)
                ' 修改：使用IF函数检查Pre Selection对应行的P列是否为空
                .Formula = "=IF('Pre Selection'!P" & preSelectionRow & "="""","""", 'Pre Selection'!P" & preSelectionRow & ")"
            End With
        Next col
    Next key
End Sub

' 初始化工作簿时调用此函数
Public Sub InitializeWorkbook()
    Call UpdateAllTargetSheetHeaders
End Sub

' 修改：当Pre Selection的A列或P列发生变化时调用此函数
Public Sub OnPreSelectionChange(ByVal Target As Range)
    ' 如果是A列且从第2行开始，执行清空操作
    If Target.Column = 1 And Target.Row >= 2 Then
        ' 检查A列是否被清空
        If Trim(Target.value) = "" Then
            Call ClearRowData(Target.Row)
        End If
    End If

    ' 如果是P列且从第2行开始，执行联动操作
    If Target.Column = 16 And Target.Row >= 2 Then ' P列是第16列
        Call UpdateCheckboxesForRow(Target.Row)
    End If
End Sub

' 测试单个工作表的公式设置 - 修改为基于P列
Public Sub TestSingleSheetFormula()
    Dim wsTarget As Worksheet
    Set wsTarget = ThisWorkbook.Sheets("Group Hospital & Surgical")

    ' 手动设置D1的公式进行测试 - 修改为引用P2
    With wsTarget.Cells(1, 4) ' D1
        .Formula = "=IF('Pre Selection'!P2="""","""", 'Pre Selection'!P2)"
    End With

    ' 检查公式是否正确设置
    Debug.Print "D1公式: " & wsTarget.Cells(1, 4).Formula
    Debug.Print "D1值: " & wsTarget.Cells(1, 4).value
End Sub

' 调试函数：检查所有目标Sheet的公式设置
Public Sub DebugFormulas()
    Dim mapping As Object
    Set mapping = GetSheetMapping()

    Dim key As Variant
    For Each key In mapping.Keys
        Dim sheetInfo As Variant
        sheetInfo = mapping(key)
        Dim wsTarget As Worksheet
        Set wsTarget = ThisWorkbook.Sheets(sheetInfo(0))

        Debug.Print "工作表: " & sheetInfo(0)
        Debug.Print "D1公式: " & wsTarget.Cells(1, 4).Formula
        Debug.Print "D1值: " & wsTarget.Cells(1, 4).value
        Debug.Print "---"
    Next key
End Sub

' 手动运行此函数来初始化所有公式
Public Sub ManualInitialize()
    Call InitializeWorkbook
    MsgBox "所有目标Sheet的表头公式已初始化完成！现在基于P列进行联动控制！"
End Sub

' 新增：手动测试清空功能
Public Sub TestClearFunction()
    ' 测试清空第2行数据
    Call ClearRowData(2)
    MsgBox "第2行数据已清空！"
End Sub

' 新增：批量更新现有数据以适应新的P列控制逻辑
Public Sub MigrateFromAColumnToPColumn()
    Dim wsPre As Worksheet
    Set wsPre = ThisWorkbook.Sheets("Pre Selection")

    Dim lastRow As Long
    lastRow = wsPre.Cells(wsPre.Rows.Count, 1).End(xlUp).Row

    Dim i As Long
    For i = 2 To lastRow
        ' 如果A列有值但P列没有值，将A列的值复制到P列
        If Trim(wsPre.Cells(i, 1).value) <> "" And Trim(wsPre.Cells(i, 16).value) = "" Then
            wsPre.Cells(i, 16).value = wsPre.Cells(i, 1).value
            ' 清空A列的值
            wsPre.Cells(i, 1).ClearContents
            ' 更新该行的复选框
            Call UpdateCheckboxesForRow(i)
        End If
    Next i

    MsgBox "数据迁移完成！现在所有联动控制都基于P列。"
End Sub

' 新增：清空A列时同时清空同行其他列的函数
Public Sub ClearRowData(ByVal rowNum As Long)
    Dim wsPre As Worksheet
    Set wsPre = ThisWorkbook.Sheets("Pre Selection")
    
    ' 清空A列到Z列的所有内容（除了第一行表头）
    If rowNum >= 2 Then
        Dim col As Integer
        For col = 1 To 26 ' A列到Z列
            wsPre.Cells(rowNum, col).ClearContents
        Next col
        
        ' 删除该行的所有复选框
        Dim chk As CheckBox
        For Each chk In wsPre.CheckBoxes
            If Not chk Is Nothing Then
                If chk.TopLeftCell.Row = rowNum Then
                    chk.Delete
                End If
            End If
        Next chk
        
        ' 清空目标Sheet的对应内容
        Call ClearTargetSheetContentsAndColors(rowNum)
        Call UpdateAllTargetSheetHeaders
    End If
End Sub
